<?php
session_start();
include '../config.php';

// تشخيص سريع (سيتم حذفه)
if (isset($_GET['debug'])) {
    echo "<pre>";
    echo "Session Debug:\n";
    echo "admin_logged_in: " . (isset($_SESSION['admin_logged_in']) ? ($_SESSION['admin_logged_in'] ? 'true' : 'false') : 'not set') . "\n";
    echo "admin_id: " . ($_SESSION['admin_id'] ?? 'not set') . "\n";
    echo "admin_username: " . ($_SESSION['admin_username'] ?? 'not set') . "\n";
    echo "isLoggedIn(): " . (isLoggedIn() ? 'true' : 'false') . "\n";
    echo "Session ID: " . session_id() . "\n";
    echo "</pre>";
    exit;
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// جلب الإحصائيات
try {
    // عدد المشاريع
    $stmt = $pdo->query("SELECT COUNT(*) FROM projects WHERE status = 'active'");
    $projectsCount = $stmt->fetchColumn();

    // عدد الرسائل الجديدة
    $stmt = $pdo->query("SELECT COUNT(*) FROM contact_messages WHERE status = 'new'");
    $newMessagesCount = $stmt->fetchColumn();

    // عدد الرسائل الإجمالي
    $stmt = $pdo->query("SELECT COUNT(*) FROM contact_messages");
    $totalMessagesCount = $stmt->fetchColumn();

    // أحدث الرسائل
    $stmt = $pdo->query("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
    $recentMessages = $stmt->fetchAll();

    // أحدث المشاريع
    $stmt = $pdo->query("SELECT * FROM projects ORDER BY created_at DESC LIMIT 5");
    $recentProjects = $stmt->fetchAll();

} catch(PDOException $e) {
    $projectsCount = 0;
    $newMessagesCount = 0;
    $totalMessagesCount = 0;
    $recentMessages = [];
    $recentProjects = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - شركة نقرة للتسويق الإلكتروني</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo2.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/logo2.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <?php include 'includes/admin-styles.php'; ?>

    <style>
        :root {
            --gradient-success: linear-gradient(135deg, var(--success-color), #059669);
            --gradient-danger: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        .sidebar {
            background: var(--gradient-primary);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-brand img {
            width: 50px;
            height: 50px;
            margin-left: 15px;
        }

        .sidebar-brand h4 {
            color: var(--primary-color);
            font-weight: 800;
            font-size: 1.2rem;
            margin: 0;
        }

        .admin-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .sidebar-nav {
            list-style: none;
            padding: 1rem 0;
        }

        .sidebar-nav li {
            margin-bottom: 0.5rem;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
            position: relative;
        }

        .sidebar-nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
        }

        .sidebar-nav a:hover::before,
        .sidebar-nav a.active::before {
            transform: scaleX(1);
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            color: white;
            border-right-color: var(--accent-color);
        }

        .sidebar-nav a i {
            margin-left: 15px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
            position: relative;
            z-index: 2;
        }

        .sidebar-nav a span {
            position: relative;
            z-index: 2;
            font-weight: 500;
        }

        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .header h1 {
            margin: 0;
            color: var(--text-color);
            font-weight: 800;
            font-size: 1.8rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .stat-card.projects::before {
            background: var(--gradient-primary);
        }

        .stat-card.messages::before {
            background: var(--gradient-success);
        }

        .stat-card.new-messages::before {
            background: var(--gradient-accent);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 1.8rem;
            color: white;
            position: relative;
        }

        .stat-icon::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: inherit;
            opacity: 0.2;
            transform: scale(1.5);
        }

        .stat-icon.projects {
            background: var(--gradient-primary);
        }

        .stat-icon.messages {
            background: var(--gradient-success);
        }

        .stat-icon.new-messages {
            background: var(--gradient-accent);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-weight: 600;
            font-size: 1.1rem;
        }
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .content-card {
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .content-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .content-card-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
            font-weight: 700;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
        }

        .content-card-header i {
            margin-left: 10px;
            font-size: 1.3rem;
        }

        .content-card-body {
            padding: 2rem;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 1.5rem 0;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.3s ease;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item:hover {
            background: var(--light-bg);
            margin: 0 -2rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        .list-item-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: var(--light-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .list-item-content h6 {
            margin: 0 0 0.5rem;
            font-weight: 700;
            color: var(--text-color);
        }

        .list-item-content p {
            margin: 0;
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.4;
        }

        .quick-action-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            text-decoration: none;
            color: var(--text-color);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            display: block;
            height: 100%;
        }

        .quick-action-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            color: var(--text-color);
        }

        .quick-action-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .quick-action-card h6 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .quick-action-card p {
            font-size: 0.9rem;
            color: #6b7280;
            margin: 0;
        }

        .btn {
            border-radius: 50px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .badge {
            border-radius: 50px;
            padding: 0.5rem 1rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .header {
                padding: 1rem;
            }

            .header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="header">
            <div style="display: flex; align-items: center;">
                <img src="../assets/images/logo2.png" alt="Logo" style="width: 40px; height: 40px; margin-left: 15px;">
                <h1>مرحباً بك في لوحة التحكم</h1>
            </div>
            <div class="header-actions">
                <a href="../index.php" target="_blank" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-eye me-1"></i>
                    معاينة الموقع
                </a>
                <span class="badge bg-primary"><?php echo date('Y-m-d H:i'); ?></span>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card projects">
                <div class="stat-icon projects">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <div class="stat-number"><?php echo $projectsCount; ?></div>
                <div class="stat-label">المشاريع النشطة</div>
            </div>

            <div class="stat-card messages">
                <div class="stat-icon messages">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stat-number"><?php echo $totalMessagesCount; ?></div>
                <div class="stat-label">إجمالي الرسائل</div>
            </div>

            <div class="stat-card new-messages">
                <div class="stat-icon new-messages">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-number"><?php echo $newMessagesCount; ?></div>
                <div class="stat-label">رسائل جديدة</div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Recent Projects -->
            <div class="content-card">
                <div class="content-card-header">
                    <i class="fas fa-project-diagram"></i>
                    أحدث المشاريع
                </div>
                <div class="content-card-body">
                    <?php if (empty($recentProjects)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-folder-open fa-3x mb-3"></i>
                            <p>لا توجد مشاريع حالياً</p>
                            <a href="projects.php" class="btn btn-primary btn-sm">إضافة مشروع جديد</a>
                        </div>
                    <?php else: ?>
                        <?php foreach (array_slice($recentProjects, 0, 5) as $project): ?>
                            <div class="list-item">
                                <div class="list-item-icon">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="list-item-content">
                                    <h6><?php echo htmlspecialchars($project['title']); ?></h6>
                                    <p><?php echo date('Y-m-d', strtotime($project['created_at'])); ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center mt-3">
                            <a href="projects.php" class="btn btn-outline-primary btn-sm">عرض جميع المشاريع</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Messages -->
            <div class="content-card">
                <div class="content-card-header">
                    <i class="fas fa-envelope"></i>
                    أحدث الرسائل
                </div>
                <div class="content-card-body">
                    <?php if (empty($recentMessages)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>لا توجد رسائل جديدة</p>
                        </div>
                    <?php else: ?>
                        <?php foreach (array_slice($recentMessages, 0, 5) as $message): ?>
                            <div class="list-item">
                                <div class="list-item-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="list-item-content">
                                    <h6><?php echo htmlspecialchars($message['name']); ?></h6>
                                    <p><?php echo htmlspecialchars(substr($message['message'], 0, 50)) . '...'; ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="text-center mt-3">
                            <a href="messages.php" class="btn btn-outline-success btn-sm">عرض جميع الرسائل</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-3 mb-3">
                <a href="projects.php" class="quick-action-card">
                    <div class="quick-action-icon bg-primary">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h6>إضافة مشروع</h6>
                    <p>إضافة مشروع جديد</p>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="messages.php" class="quick-action-card">
                    <div class="quick-action-icon bg-success">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h6>الرسائل</h6>
                    <p>عرض الرسائل الجديدة</p>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="settings.php" class="quick-action-card">
                    <div class="quick-action-icon bg-warning">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h6>الإعدادات</h6>
                    <p>إعدادات الموقع</p>
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="../index.php" target="_blank" class="quick-action-card">
                    <div class="quick-action-icon bg-info">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h6>معاينة الموقع</h6>
                    <p>عرض الموقع الرئيسي</p>
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
