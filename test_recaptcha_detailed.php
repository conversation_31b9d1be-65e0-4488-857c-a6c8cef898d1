<?php
session_start();
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مفصل للكابتشا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', sans-serif; }
        .test-container { max-width: 800px; margin: 50px auto; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="test-container">
            <h1 class="text-center mb-4">تشخيص مفصل للكابتشا</h1>
            
            <!-- معلومات الإعدادات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>إعدادات الكابتشا</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>مفعلة:</strong> 
                                <span class="<?php echo $recaptcha_settings['enabled'] ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo $recaptcha_settings['enabled'] ? 'نعم ✓' : 'لا ✗'; ?>
                                </span>
                            </p>
                            <p><strong>Site Key:</strong> 
                                <span class="<?php echo !empty($recaptcha_settings['site_key']) ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo !empty($recaptcha_settings['site_key']) ? 'موجود ✓' : 'غير موجود ✗'; ?>
                                </span>
                            </p>
                            <p><strong>Secret Key:</strong> 
                                <span class="<?php echo !empty($recaptcha_settings['secret_key']) ? 'status-good' : 'status-bad'; ?>">
                                    <?php echo !empty($recaptcha_settings['secret_key']) ? 'موجود ✓' : 'غير موجود ✗'; ?>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>النطاق:</strong> <?php echo $_SERVER['HTTP_HOST']; ?></p>
                            <p><strong>البروتوكول:</strong> <?php echo isset($_SERVER['HTTPS']) ? 'HTTPS' : 'HTTP'; ?></p>
                            <p><strong>IP الخادم:</strong> <?php echo $_SERVER['SERVER_ADDR'] ?? 'غير معروف'; ?></p>
                        </div>
                    </div>
                    
                    <?php if (!empty($recaptcha_settings['site_key'])): ?>
                    <div class="mt-3">
                        <strong>Site Key الكامل:</strong>
                        <div class="code-block"><?php echo htmlspecialchars($recaptcha_settings['site_key']); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- اختبار تحميل السكريبت -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>اختبار تحميل سكريبت الكابتشا</h5>
                </div>
                <div class="card-body">
                    <div id="script-status" class="alert alert-info">
                        جاري التحقق من تحميل السكريبت...
                    </div>
                    
                    <p><strong>رابط السكريبت:</strong></p>
                    <div class="code-block">https://www.google.com/recaptcha/api.js</div>
                    
                    <p class="mt-3"><strong>كود HTML المُولد:</strong></p>
                    <div class="code-block"><?php echo htmlspecialchars(getRecaptchaScript($recaptcha_settings['enabled'])); ?></div>
                </div>
            </div>
            
            <!-- اختبار الكابتشا -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>اختبار عرض الكابتشا</h5>
                </div>
                <div class="card-body">
                    <?php if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])): ?>
                    
                    <div class="alert alert-info">
                        <strong>يجب أن تظهر الكابتشا أدناه:</strong>
                    </div>
                    
                    <div class="border p-3 bg-white rounded text-center">
                        <div id="recaptcha-container">
                            <div class="g-recaptcha" data-sitekey="<?php echo htmlspecialchars($recaptcha_settings['site_key']); ?>"></div>
                        </div>
                        <div id="recaptcha-fallback" style="display: none;" class="alert alert-warning mt-3">
                            ❌ الكابتشا لم تُحمل - قد تكون هناك مشكلة في المفاتيح أو الاتصال
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <strong>كود HTML للكابتشا:</strong>
                        <div class="code-block">&lt;div class="g-recaptcha" data-sitekey="<?php echo htmlspecialchars($recaptcha_settings['site_key']); ?>"&gt;&lt;/div&gt;</div>
                    </div>
                    
                    <?php else: ?>
                    <div class="alert alert-warning">
                        الكابتشا غير مفعلة أو Site Key غير موجود
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- اختبار الاتصال -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>اختبار الاتصال بخوادم Google</h5>
                </div>
                <div class="card-body">
                    <div id="connection-test" class="alert alert-info">
                        جاري اختبار الاتصال...
                    </div>
                </div>
            </div>
            
            <!-- الحلول المقترحة -->
            <div class="card">
                <div class="card-header">
                    <h5>الحلول المقترحة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إذا كانت الكابتشا لا تظهر:</h6>
                            <ol>
                                <li>تحقق من أن المفاتيح صحيحة للنطاق</li>
                                <li>تأكد من تسجيل النطاق في Google reCAPTCHA</li>
                                <li>تحقق من عدم حظر سكريبت Google</li>
                                <li>جرب إلغاء تفعيل الكابتشا مؤقتاً</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>روابط مفيدة:</h6>
                            <ul>
                                <li><a href="https://www.google.com/recaptcha/admin" target="_blank">Google reCAPTCHA Console</a></li>
                                <li><a href="fix_recaptcha.php">إصلاح سريع للكابتشا</a></li>
                                <li><a href="admin/settings.php">إعدادات النظام</a></li>
                                <li><a href="admin/login.php">اختبار تسجيل الدخول</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تحميل سكريبت الكابتشا -->
    <?php echo getRecaptchaScript($recaptcha_settings['enabled']); ?>
    
    <script>
        // اختبار تحميل السكريپت
        function checkScriptLoading() {
            const statusDiv = document.getElementById('script-status');
            
            if (typeof grecaptcha !== 'undefined') {
                statusDiv.className = 'alert alert-success';
                statusDiv.innerHTML = '✅ سكريپت الكابتشا محمل بنجاح';
                
                // اختبار عرض الكابتشا
                setTimeout(checkRecaptchaRender, 2000);
            } else {
                statusDiv.className = 'alert alert-danger';
                statusDiv.innerHTML = '❌ سكريپت الكابتشا غير محمل - تحقق من الاتصال بالإنترنت أو حظر الإعلانات';
                
                // إظهار رسالة بديلة للكابتشا
                const fallback = document.getElementById('recaptcha-fallback');
                if (fallback) fallback.style.display = 'block';
            }
        }
        
        // اختبار عرض الكابتشا
        function checkRecaptchaRender() {
            const container = document.getElementById('recaptcha-container');
            const fallback = document.getElementById('recaptcha-fallback');
            
            if (container && container.querySelector('iframe')) {
                // الكابتشا ظهرت بنجاح
                console.log('✅ الكابتشا ظهرت بنجاح');
            } else if (fallback) {
                fallback.style.display = 'block';
            }
        }
        
        // اختبار الاتصال بخوادم Google
        function testConnection() {
            const testDiv = document.getElementById('connection-test');
            
            fetch('https://www.google.com/recaptcha/api.js', { mode: 'no-cors' })
                .then(() => {
                    testDiv.className = 'alert alert-success';
                    testDiv.innerHTML = '✅ الاتصال بخوادم Google يعمل بشكل صحيح';
                })
                .catch(() => {
                    testDiv.className = 'alert alert-danger';
                    testDiv.innerHTML = '❌ لا يمكن الوصول لخوادم Google - تحقق من الاتصال أو الحظر';
                });
        }
        
        // تشغيل الاختبارات
        window.onload = function() {
            setTimeout(checkScriptLoading, 1000);
            testConnection();
        };
    </script>
</body>
</html>
