<?php
session_start();
define('SECURE_ACCESS', true);
include 'config.php';

$message = '';
$error = '';

// معالجة الطلبات
if ($_POST) {
    try {
        if (isset($_POST['disable_recaptcha'])) {
            // إلغاء تفعيل الكابتشا
            $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");
            
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
            $stmt->execute(['recaptcha_enabled', '0', '0']);
            
            $message = 'تم إلغاء تفعيل الكابتشا بنجاح! يمكنك الآن تسجيل الدخول بدون كابتشا.';
            
        } elseif (isset($_POST['enable_recaptcha'])) {
            // تفعيل الكابتشا مع مفاتيح جديدة
            $site_key = trim($_POST['site_key'] ?? '');
            $secret_key = trim($_POST['secret_key'] ?? '');
            
            if (empty($site_key) || empty($secret_key)) {
                $error = 'يرجى إدخال كلا المفتاحين (Site Key و Secret Key)';
            } else {
                $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )");
                
                $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                $stmt->execute(['recaptcha_enabled', '1', '1']);
                $stmt->execute(['recaptcha_site_key', $site_key, $site_key]);
                $stmt->execute(['recaptcha_secret_key', $secret_key, $secret_key]);
                
                $message = 'تم تفعيل الكابتشا بنجاح مع المفاتيح الجديدة!';
            }
        }
    } catch(PDOException $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
$current_settings = [
    'enabled' => false,
    'site_key' => '',
    'secret_key' => ''
];

try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('recaptcha_enabled', 'recaptcha_site_key', 'recaptcha_secret_key')");
    $settings = $stmt->fetchAll();
    
    foreach ($settings as $setting) {
        switch ($setting['setting_key']) {
            case 'recaptcha_enabled':
                $current_settings['enabled'] = (bool)$setting['setting_value'];
                break;
            case 'recaptcha_site_key':
                $current_settings['site_key'] = $setting['setting_value'];
                break;
            case 'recaptcha_secret_key':
                $current_settings['secret_key'] = $setting['setting_value'];
                break;
        }
    }
} catch(PDOException $e) {
    // جدول غير موجود
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة الكابتشا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Tajawal', sans-serif; }
        .fix-container { max-width: 800px; margin: 50px auto; }
        .status-card { margin-bottom: 20px; }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="fix-container">
            <h1 class="text-center mb-4">إصلاح مشكلة الكابتشا</h1>
            
            <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $message; ?>
            </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-times-circle"></i> <?php echo $error; ?>
            </div>
            <?php endif; ?>
            
            <!-- الحالة الحالية -->
            <div class="card status-card">
                <div class="card-header">
                    <h5>الحالة الحالية للكابتشا</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>مفعلة:</strong>
                            <span class="badge <?php echo $current_settings['enabled'] ? 'bg-success' : 'bg-secondary'; ?>">
                                <?php echo $current_settings['enabled'] ? 'نعم' : 'لا'; ?>
                            </span>
                        </div>
                        <div class="col-md-4">
                            <strong>Site Key:</strong>
                            <span class="badge <?php echo !empty($current_settings['site_key']) ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo !empty($current_settings['site_key']) ? 'موجود' : 'غير موجود'; ?>
                            </span>
                        </div>
                        <div class="col-md-4">
                            <strong>Secret Key:</strong>
                            <span class="badge <?php echo !empty($current_settings['secret_key']) ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo !empty($current_settings['secret_key']) ? 'موجود' : 'غير موجود'; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الحل السريع -->
            <div class="card">
                <div class="card-header">
                    <h5>الحل السريع</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>الحل الأول: إلغاء تفعيل الكابتشا</h6>
                            <p class="text-muted">هذا سيسمح بتسجيل الدخول بدون كابتشا</p>
                            <form method="POST">
                                <button type="submit" name="disable_recaptcha" class="btn btn-warning">
                                    إلغاء تفعيل الكابتشا
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <h6>الحل الثاني: تفعيل الكابتشا بمفاتيح صحيحة</h6>
                            <form method="POST">
                                <div class="mb-2">
                                    <input type="text" class="form-control form-control-sm" name="site_key" 
                                           placeholder="Site Key" value="<?php echo htmlspecialchars($current_settings['site_key']); ?>">
                                </div>
                                <div class="mb-2">
                                    <input type="text" class="form-control form-control-sm" name="secret_key" 
                                           placeholder="Secret Key" value="<?php echo htmlspecialchars($current_settings['secret_key']); ?>">
                                </div>
                                <button type="submit" name="enable_recaptcha" class="btn btn-success">
                                    تفعيل الكابتشا
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="admin/login.php" class="btn btn-primary">اختبار تسجيل الدخول</a>
                <a href="admin/settings.php" class="btn btn-secondary">إعدادات متقدمة</a>
                <a href="debug_recaptcha.php" class="btn btn-info">تشخيص المشكلة</a>
            </div>
        </div>
    </div>
</body>
</html>
