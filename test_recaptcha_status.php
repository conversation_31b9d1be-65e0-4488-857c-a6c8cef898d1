<?php
session_start();
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حالة الكابتشا</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Tajawal', sans-serif; }
        .status-card { margin: 20px auto; max-width: 600px; }
        .status-good { border-left: 5px solid #28a745; }
        .status-warning { border-left: 5px solid #ffc107; }
        .status-error { border-left: 5px solid #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">اختبار حالة الكابتشا</h1>
                
                <div class="card status-card">
                    <div class="card-header">
                        <h5>إعدادات الكابتشا الحالية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>حالة التفعيل:</strong>
                                <?php if ($recaptcha_settings['enabled']): ?>
                                    <span class="badge bg-success">مفعل</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير مفعل</span>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Site Key:</strong>
                                <?php if (!empty($recaptcha_settings['site_key'])): ?>
                                    <span class="badge bg-success">موجود (<?php echo strlen($recaptcha_settings['site_key']); ?> حرف)</span>
                                    <br><small class="text-muted"><?php echo substr($recaptcha_settings['site_key'], 0, 20) . '...'; ?></small>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <strong>Secret Key:</strong>
                                <?php if (!empty($recaptcha_settings['secret_key'])): ?>
                                    <span class="badge bg-success">موجود (<?php echo strlen($recaptcha_settings['secret_key']); ?> حرف)</span>
                                    <br><small class="text-muted"><?php echo substr($recaptcha_settings['secret_key'], 0, 20) . '...'; ?></small>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <strong>النطاق الحالي:</strong>
                                <span class="badge bg-info"><?php echo $_SERVER['HTTP_HOST']; ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اختبار الكابتشا في تسجيل الدخول -->
                <div class="card status-card <?php echo ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])) ? 'status-good' : 'status-error'; ?>">
                    <div class="card-header">
                        <h5>اختبار الكابتشا في تسجيل الدخول</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                الكابتشا مفعلة ومُعدة بشكل صحيح في تسجيل الدخول
                            </div>
                            
                            <!-- عرض الكابتشا كما تظهر في تسجيل الدخول -->
                            <div class="border p-3 bg-white rounded">
                                <h6>معاينة الكابتشا:</h6>
                                <div class="g-recaptcha" data-sitekey="<?php echo htmlspecialchars($recaptcha_settings['site_key']); ?>"></div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle"></i>
                                الكابتشا غير مُعدة بشكل صحيح في تسجيل الدخول
                                <br><small>
                                    <?php if (!$recaptcha_settings['enabled']): ?>
                                        - الكابتشا غير مفعلة
                                    <?php endif; ?>
                                    <?php if (empty($recaptcha_settings['site_key'])): ?>
                                        - Site Key غير موجود
                                    <?php endif; ?>
                                    <?php if (empty($recaptcha_settings['secret_key'])): ?>
                                        - Secret Key غير موجود
                                    <?php endif; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- اختبار الكابتشا في النماذج الأخرى -->
                <div class="card status-card status-good">
                    <div class="card-header">
                        <h5>اختبار الكابتشا في النماذج الأخرى</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            الكابتشا تعمل بشكل صحيح في:
                            <ul class="mb-0 mt-2">
                                <li>نموذج التواصل (contact.php)</li>
                                <li>الصفحة الرئيسية (index.php)</li>
                                <li>جميع النماذج الأخرى</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="admin/login.php" class="btn btn-primary">اختبار تسجيل الدخول</a>
                    <a href="contact.php" class="btn btn-secondary">اختبار نموذج التواصل</a>
                    <a href="admin/settings.php" class="btn btn-warning">إعدادات الكابتشا</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo getRecaptchaScript($recaptcha_settings['enabled']); ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
