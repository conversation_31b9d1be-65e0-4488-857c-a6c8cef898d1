<?php
session_start();
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

$test_results = [];

// اختبار 1: جلب إعدادات الكابتشا
$recaptcha_settings = getRecaptchaSettings($pdo);
$test_results[] = [
    'test' => 'جلب إعدادات الكابتشا',
    'result' => 'نجح',
    'details' => 'مفعل: ' . ($recaptcha_settings['enabled'] ? 'نعم' : 'لا') . 
                ', Site Key: ' . (!empty($recaptcha_settings['site_key']) ? 'موجود' : 'غير موجود') . 
                ', Secret Key: ' . (!empty($recaptcha_settings['secret_key']) ? 'موجود' : 'غير موجود')
];

// اختبار 2: اختبار دالة validateRecaptchaInForm بدون POST data
$validation_result = validateRecaptchaInForm($pdo);
$test_results[] = [
    'test' => 'اختبار validateRecaptchaInForm (بدون POST)',
    'result' => $validation_result ? 'نجح' : 'فشل',
    'details' => $validation_result ? 'الدالة تسمح بالمرور' : 'الدالة ترفض المرور'
];

// اختبار 3: محاكاة POST request بدون كابتشا
$_POST = ['username' => 'test', 'password' => 'test'];
$validation_result_with_post = validateRecaptchaInForm($pdo);
$test_results[] = [
    'test' => 'اختبار validateRecaptchaInForm (مع POST بدون كابتشا)',
    'result' => $validation_result_with_post ? 'نجح' : 'فشل',
    'details' => $validation_result_with_post ? 'الدالة تسمح بالمرور' : 'الدالة ترفض المرور'
];

// اختبار 4: التحقق من وجود جدول admin
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM admin");
    $admin_count = $stmt->fetchColumn();
    $test_results[] = [
        'test' => 'التحقق من جدول المديرين',
        'result' => 'نجح',
        'details' => "يوجد {$admin_count} مدير في النظام"
    ];
} catch(PDOException $e) {
    $test_results[] = [
        'test' => 'التحقق من جدول المديرين',
        'result' => 'فشل',
        'details' => 'خطأ: ' . $e->getMessage()
    ];
}

// تنظيف POST data
unset($_POST);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Tajawal', sans-serif; }
        .test-container { max-width: 800px; margin: 50px auto; }
        .test-success { background-color: #d4edda; border-color: #c3e6cb; }
        .test-fail { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="test-container">
            <h1 class="text-center mb-4">اختبار تسجيل الدخول</h1>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>نتائج الاختبارات</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results as $test): ?>
                    <div class="alert <?php echo $test['result'] === 'نجح' ? 'test-success' : 'test-fail'; ?>">
                        <strong><?php echo $test['test']; ?>:</strong> 
                        <span class="badge <?php echo $test['result'] === 'نجح' ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test['result']; ?>
                        </span>
                        <br><small><?php echo $test['details']; ?></small>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>اختبار تسجيل الدخول المباشر</h5>
                </div>
                <div class="card-body">
                    <p>يمكنك الآن اختبار تسجيل الدخول:</p>
                    <div class="d-grid gap-2">
                        <a href="admin/login.php" class="btn btn-primary">
                            الذهاب لصفحة تسجيل الدخول
                        </a>
                    </div>
                    
                    <hr>
                    
                    <h6>معلومات مفيدة:</h6>
                    <ul>
                        <li>إذا كانت جميع الاختبارات نجحت، يجب أن يعمل تسجيل الدخول الآن</li>
                        <li>إذا كانت الكابتشا غير مفعلة، لن تظهر في نموذج تسجيل الدخول</li>
                        <li>إذا كانت الكابتشا مفعلة ولكن بدون مفاتيح، ستظهر ولكن لن تمنع تسجيل الدخول</li>
                    </ul>
                </div>
            </div>
            
            <div class="text-center">
                <a href="fix_recaptcha.php" class="btn btn-warning">إصلاح إعدادات الكابتشا</a>
                <a href="debug_recaptcha.php" class="btn btn-info">تشخيص المشكلة</a>
                <a href="admin/settings.php" class="btn btn-secondary">الإعدادات</a>
            </div>
        </div>
    </div>
</body>
</html>
