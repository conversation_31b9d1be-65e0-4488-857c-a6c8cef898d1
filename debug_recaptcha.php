<?php
session_start();
define('SECURE_ACCESS', true);
include 'config.php';

echo "<h2>تشخيص مشكلة الكابتشا</h2>";

try {
    // التحقق من وجود جدول الإعدادات
    $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
    $table_exists = $stmt->rowCount() > 0;
    
    echo "<p><strong>جدول الإعدادات موجود:</strong> " . ($table_exists ? "نعم" : "لا") . "</p>";
    
    if ($table_exists) {
        // جلب جميع إعدادات الكابتشا
        $stmt = $pdo->query("SELECT * FROM settings WHERE setting_key LIKE 'recaptcha%'");
        $settings = $stmt->fetchAll();
        
        echo "<h3>إعدادات الكابتشا في قاعدة البيانات:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>المفتاح</th><th>القيمة</th><th>تاريخ الإنشاء</th><th>تاريخ التحديث</th></tr>";
        
        foreach ($settings as $setting) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['setting_value']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['created_at']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['updated_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // تحليل الإعدادات
        $recaptcha_enabled = false;
        $site_key = '';
        $secret_key = '';
        
        foreach ($settings as $setting) {
            switch ($setting['setting_key']) {
                case 'recaptcha_enabled':
                    $recaptcha_enabled = (bool)$setting['setting_value'];
                    break;
                case 'recaptcha_site_key':
                    $site_key = $setting['setting_value'];
                    break;
                case 'recaptcha_secret_key':
                    $secret_key = $setting['setting_value'];
                    break;
            }
        }
        
        echo "<h3>تحليل الإعدادات:</h3>";
        echo "<p><strong>الكابتشا مفعلة:</strong> " . ($recaptcha_enabled ? "نعم" : "لا") . "</p>";
        echo "<p><strong>Site Key موجود:</strong> " . (!empty($site_key) ? "نعم (" . strlen($site_key) . " حرف)" : "لا") . "</p>";
        echo "<p><strong>Secret Key موجود:</strong> " . (!empty($secret_key) ? "نعم (" . strlen($secret_key) . " حرف)" : "لا") . "</p>";
        
        echo "<h3>المشكلة:</h3>";
        if ($recaptcha_enabled && empty($secret_key)) {
            echo "<div style='background: #ffcccc; padding: 10px; border: 1px solid #ff0000;'>";
            echo "<strong>المشكلة موجودة!</strong><br>";
            echo "الكابتشا مفعلة ولكن Secret Key غير موجود، مما يمنع تسجيل الدخول.";
            echo "</div>";
        } elseif (!$recaptcha_enabled) {
            echo "<div style='background: #ccffcc; padding: 10px; border: 1px solid #00ff00;'>";
            echo "<strong>لا توجد مشكلة!</strong><br>";
            echo "الكابتشا غير مفعلة، لذا يجب أن يعمل تسجيل الدخول بدونها.";
            echo "</div>";
        } else {
            echo "<div style='background: #ccffcc; padding: 10px; border: 1px solid #00ff00;'>";
            echo "<strong>الإعدادات صحيحة!</strong><br>";
            echo "الكابتشا مفعلة ومُعدة بشكل صحيح.";
            echo "</div>";
        }
        
    } else {
        echo "<p><strong>جدول الإعدادات غير موجود - الكابتشا غير مفعلة</strong></p>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'><strong>خطأ:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>الحلول المقترحة:</h3>";
echo "<ol>";
echo "<li><a href='admin/settings.php'>الذهاب لإعدادات الكابتشا وإلغاء تفعيلها</a></li>";
echo "<li><a href='admin/settings.php'>أو إضافة مفاتيح الكابتشا الصحيحة</a></li>";
echo "<li><a href='admin/login.php'>اختبار تسجيل الدخول</a></li>";
echo "</ol>";
?>
