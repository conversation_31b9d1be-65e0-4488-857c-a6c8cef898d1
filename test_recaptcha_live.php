<?php
session_start();
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);

$test_message = '';
if ($_POST) {
    $test_message = 'تم إرسال النموذج!<br>';
    $test_message .= 'اسم المستخدم: ' . ($_POST['username'] ?? 'غير موجود') . '<br>';
    $test_message .= 'g-recaptcha-response: ' . ($_POST['g-recaptcha-response'] ?? 'غير موجود') . '<br>';
    
    $validation_result = validateRecaptchaInForm($pdo);
    $test_message .= 'نتيجة التحقق من الكابتشا: ' . ($validation_result ? 'نجح' : 'فشل');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الكابتشا المباشر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    
    <?php echo getRecaptchaScript($recaptcha_settings['enabled']); ?>
    
    <style>
        body { font-family: 'Tajawal', sans-serif; }
        .test-container { max-width: 600px; margin: 50px auto; }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="test-container">
            <h1 class="text-center mb-4">اختبار الكابتشا المباشر</h1>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>معلومات الكابتشا</h5>
                </div>
                <div class="card-body">
                    <p><strong>مفعلة:</strong> <?php echo $recaptcha_settings['enabled'] ? 'نعم' : 'لا'; ?></p>
                    <p><strong>Site Key:</strong> <?php echo !empty($recaptcha_settings['site_key']) ? 'موجود (' . strlen($recaptcha_settings['site_key']) . ' حرف)' : 'غير موجود'; ?></p>
                    <p><strong>Secret Key:</strong> <?php echo !empty($recaptcha_settings['secret_key']) ? 'موجود (' . strlen($recaptcha_settings['secret_key']) . ' حرف)' : 'غير موجود'; ?></p>
                    
                    <?php if (!empty($recaptcha_settings['site_key'])): ?>
                    <p><strong>Site Key:</strong> <code><?php echo htmlspecialchars($recaptcha_settings['site_key']); ?></code></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($test_message): ?>
            <div class="alert alert-info">
                <h6>نتيجة الاختبار:</h6>
                <?php echo $test_message; ?>
            </div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-header">
                    <h5>نموذج اختبار الكابتشا</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" value="test" required>
                        </div>
                        
                        <?php if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])): ?>
                        <div class="mb-3">
                            <label class="form-label">التحقق من أنك لست روبوت</label>
                            <div class="g-recaptcha" data-sitekey="<?php echo htmlspecialchars($recaptcha_settings['site_key']); ?>"></div>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            الكابتشا غير مفعلة أو غير مُعدة بشكل صحيح
                        </div>
                        <?php endif; ?>
                        
                        <button type="submit" class="btn btn-primary">اختبار</button>
                    </form>
                </div>
            </div>
            
            <div class="mt-4">
                <h6>معلومات تقنية:</h6>
                <ul>
                    <li>النطاق الحالي: <?php echo $_SERVER['HTTP_HOST']; ?></li>
                    <li>البروتوكول: <?php echo isset($_SERVER['HTTPS']) ? 'HTTPS' : 'HTTP'; ?></li>
                    <li>User Agent: <?php echo substr($_SERVER['HTTP_USER_AGENT'], 0, 100); ?>...</li>
                </ul>
            </div>
            
            <div class="text-center mt-4">
                <a href="admin/login.php" class="btn btn-secondary">تسجيل الدخول</a>
                <a href="debug_recaptcha.php" class="btn btn-info">تشخيص</a>
                <a href="fix_recaptcha.php" class="btn btn-warning">إصلاح</a>
            </div>
        </div>
    </div>
    
    <script>
        // التحقق من تحميل سكريبت الكابتشا
        window.onload = function() {
            if (typeof grecaptcha !== 'undefined') {
                console.log('✅ سكريبت الكابتشا محمل بنجاح');
            } else {
                console.log('❌ سكريبت الكابتشا غير محمل');
                document.body.insertAdjacentHTML('beforeend', 
                    '<div class="alert alert-danger position-fixed bottom-0 start-0 m-3">' +
                    '❌ سكريبت الكابتشا غير محمل - تحقق من الاتصال بالإنترنت' +
                    '</div>'
                );
            }
        };
    </script>
</body>
</html>
